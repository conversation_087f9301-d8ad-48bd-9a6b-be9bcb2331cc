# BlogCore - نظام إدارة المستخدمين

## نظرة عامة
نظام إدارة المستخدمين المبني على Django مع استخدام نظام المصادقة الجاهز من Django.

## الميزات
- نموذج مستخدم مخصص يرث من `AbstractUser`
- حقول إضافية: رقم الهاتف، رابط الصورة
- نظام الحذف الناعم (Soft Delete)
- حقول التتبع الزمني (date_created, date_updated, date_deleted)
- استخدام UUID كمفتاح أساسي
- واجهة إدارة محسنة مع أكشن للحذف الناعم والاستعادة
- استخدام نظام Groups و Permissions الجاهز من Django

## التثبيت

### 1. إنشاء البيئة الافتراضية
```bash
python -m venv myvenv
source myvenv/bin/activate  # Linux/Mac
# أو
myvenv\Scripts\activate  # Windows
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات
```bash
cd src
python manage.py migrate
```

### 4. إنشاء مستخدم إداري
```bash
python manage.py createsuperuser
```

### 5. تشغيل الخادم
```bash
python manage.py runserver
```

## هيكل المشروع
```
BlogCore/
├── src/
│   ├── apps/
│   │   └── users/
│   │       ├── models/
│   │       │   ├── __init__.py
│   │       │   └── user.py
│   │       ├── admin.py
│   │       ├── apps.py
│   │       └── migrations/
│   ├── common/
│   │   └── models/
│   │       └── base_model.py
│   ├── BlogCore/
│   │   ├── settings.py
│   │   └── urls.py
│   └── manage.py
└── requirements.txt
```

## نموذج المستخدم

### الحقول الأساسية (من AbstractUser)
- username
- email
- first_name
- last_name
- password
- is_staff
- is_active
- is_superuser
- date_joined
- last_login

### الحقول المخصصة
- `id`: UUID (مفتاح أساسي)
- `phone_number`: رقم الهاتف (اختياري)
- `image_url`: رابط الصورة (اختياري)
- `date_created`: تاريخ الإنشاء
- `date_updated`: تاريخ آخر تحديث
- `date_deleted`: تاريخ الحذف (للحذف الناعم)

### الدوال المخصصة
- `soft_delete()`: حذف ناعم للمستخدم
- `restore()`: استعادة المستخدم المحذوف

## واجهة الإدارة

### الميزات
- عرض محسن لقائمة المستخدمين
- فلترة حسب الحالة والمجموعات
- بحث في الحقول المختلفة
- أكشن للحذف الناعم والاستعادة
- عرض الحقول الزمنية كحقول للقراءة فقط

### الوصول
- الرابط: `http://localhost:8000/admin/`
- المستخدم: `admin`
- كلمة المرور: `admin123`

## استخدام Groups و Permissions

يستخدم النظام Groups و Permissions الجاهزة من Django:

### إنشاء مجموعة جديدة
```python
from django.contrib.auth.models import Group, Permission

# إنشاء مجموعة
editors_group = Group.objects.create(name='Editors')

# إضافة صلاحيات
permission = Permission.objects.get(codename='add_user')
editors_group.permissions.add(permission)
```

### إضافة مستخدم لمجموعة
```python
from apps.users.models import User
from django.contrib.auth.models import Group

user = User.objects.get(username='username')
group = Group.objects.get(name='Editors')
user.groups.add(group)
```

## API (Django REST Framework)

النظام يوفر API كامل للتعامل مع المستخدمين والمجموعات:

### نقاط النهاية المتاحة

#### المستخدمين
- `GET /api/users/` - قائمة المستخدمين
- `POST /api/users/` - إنشاء مستخدم جديد
- `GET /api/users/{id}/` - عرض مستخدم محدد
- `PUT /api/users/{id}/` - تحديث مستخدم
- `DELETE /api/users/{id}/` - حذف ناعم لمستخدم
- `POST /api/users/{id}/restore/` - استعادة مستخدم محذوف
- `GET /api/users/deleted/` - قائمة المستخدمين المحذوفين

#### الملف الشخصي
- `GET /api/profile/` - عرض الملف الشخصي
- `PUT /api/profile/` - تحديث الملف الشخصي

#### المجموعات
- `GET /api/groups/` - قائمة المجموعات
- `POST /api/groups/` - إنشاء مجموعة جديدة
- `GET /api/groups/{id}/` - عرض مجموعة محددة
- `PUT /api/groups/{id}/` - تحديث مجموعة
- `DELETE /api/groups/{id}/` - حذف مجموعة

#### الصلاحيات
- `GET /api/permissions/` - قائمة الصلاحيات

### المصادقة
- `GET /api/auth/login/` - تسجيل الدخول
- `GET /api/auth/logout/` - تسجيل الخروج

### مثال على الاستخدام
```bash
# تسجيل الدخول
curl -X POST http://localhost:8000/api/auth/login/ \
  -d "username=admin&password=admin123"

# الحصول على قائمة المستخدمين
curl -X GET http://localhost:8000/api/users/ \
  -H "Authorization: Basic YWRtaW46YWRtaW4xMjM="

# إنشاء مستخدم جديد
curl -X POST http://localhost:8000/api/users/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "newpass123",
    "password_confirm": "newpass123",
    "first_name": "New",
    "last_name": "User"
  }'
```

## الأمان

- استخدام UUID كمفتاح أساسي لتجنب تخمين المعرفات
- نظام الحذف الناعم للحفاظ على البيانات
- استخدام نظام المصادقة والصلاحيات الجاهز من Django

## المساهمة

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push للـ branch
5. إنشاء Pull Request
