import logging
import re
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
from drf_spectacular.utils import extend_schema_field
from rest_framework_simplejwt.tokens import RefreshToken

from ..services.user_service import UserService


User = get_user_model()
logger = logging.getLogger(__name__)

class UserRegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True,
        min_length=8,
        help_text="Password must be at least 8 characters long"
    )
    password_confirm = serializers.CharField(
        write_only=True,
        help_text="Confirm your password"
    )
    class Meta:
        model = User
        fields = [
            "username", "email",
            "password", "password_confirm"
        ]
        extra_kwargs = {
            'username': {'required': True, 'allow_blank': False},
            'email': {'required': True, 'allow_blank': False},
        }
        
    def create(self, validated_data):
        username = validated_data.get("username")
        email = validated_data.get("email")
        password = validated_data.get("password")
        user = UserService.create_user(
            username=username,
            email=email,
            password=password,
        )
        self.refresh=RefreshToken.for_user(user)
        return user
    def to_representation(self, instance):
        refresh = getattr(self, "refresh", None)
        data = {
            "username": instance.username,
            "email": instance.email,
        }
        if refresh:
            data["access_token"] = str(refresh.access_token)
            data["refresh_token"] = str(refresh)
        return data
        
    
class UserModifySerializer(serializers.ModelSerializer):
   
        password = serializers.CharField(
        write_only=True,
        min_length=8,
        help_text="Password must be at least 8 characters long"
        )
        password_confirm = serializers.CharField(
        write_only=True,
        help_text="Confirm your password"
        )
        image = serializers.ImageField(
        required=False,
        allow_null=True,
        help_text="Profile image (optional)"
        )
        class Meta :
            model=User
            image = serializers.ImageField(required=False, allow_null=True)
            fields=["user_id,first_name","last_name","username","email","image_modified","image"]
            extra_kwargs = {
                'user_id':{'required':True},
                'username': {'required': True, 'allow_blank': False},
                'email': {'required': True, 'allow_blank': False},
                'first_name':{'required':True},
                'last_name':{'required':True}
            }
        def modify(self ,validated_data):
            user_id=validated_data.get('first_name')
            first_name=validated_data.get('first_name')
            last_name=validated_data.get('last_name')
            username=validated_data.get('username')
            email=validated_data.get('email')
            image=validated_data.get('image')
            image_modified=validated_data.get('image_modifided')
            try:
                UserService.modify_user(user_id,first_name,last_name,username,email,image_modified,image)
            except Exception as e:
                raise e
        
        def to_representation(self, instance):
            data = {
                "user_id":instance.id,
                "first_name":instance.first_name,
                "last_name":instance.last_name,
                "username": instance.username,
                "email": instance.email,
                "image_url":instance.image_url,
            }
            return data
            


