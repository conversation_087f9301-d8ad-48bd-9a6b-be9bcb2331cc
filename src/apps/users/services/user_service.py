import logging
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models import Q
from apps.users.models.models import User
from apps.users.exceptions.exceptions import (
    UserAlreadyExistsException,
    UserNotFoundException,
    InvalidUserDataException,
    FileUploadException,
    UserPasswordIsWrong
)
from common.services.file_service import FileService

User = get_user_model()
logger = logging.getLogger(__name__)
class UserService:

    @staticmethod
    def create_user(username,email,password):

        if User.objects.filter(username=username).exists():
            raise UserAlreadyExistsException(f"User with username '{username}' already exists")
        
        if User.objects.filter(email=email).exists():
            raise UserAlreadyExistsException(f"User with email '{email}' already exists")
        try:
            user=User.create_user(username,email,password)
            user.save()
            return user
        except Exception as e:
            logger.error(f"Failed to create user {username}: {str(e)}")
            raise InvalidUserDataException(f"Failed to create user: {str(e)}")




    @staticmethod
    def modify_user(user_id,first_name,last_name,username,email,image_modified,image):
        user=User.objects.get(id=id)

        if user is None:
            raise UserNotFoundException(f"User not found")
        if User.objects.filter(~Q(id=id), username=username).exists():
            logger.warning(f"Attempt to modify user {user_id} with existing username: {username}")
            raise UserAlreadyExistsException(f"Username '{username}' already exists")

        if User.objects.filter(~Q(user_id=user_id), email=email).exists():
            logger.warning(f"Attempt to modify user {user_id} with existing email: {email}")
            raise UserAlreadyExistsException(f"Email '{email}' already exists")
        new_image_url=None
        if(image_modified):
            try:
                new_image_url=FileService.update(user.image_url,image,"storage/public")
            except Exception as e:
                logger.error(f"Image upload failed for user {username}: {str(e)}")
                raise FileUploadException(f"Failed to upload image: {str(e)}")
        
        try:
            user.modify_user(
                first_name=first_name,
                last_name=last_name,
                username=username,
                email=email,
                image_url=new_image_url
            )
            user.save()
            return user

        except Exception as e:
            raise InvalidUserDataException(f"Failed to modify user: {str(e)}")
    

    @staticmethod 
    def modify_password(user_id,old_password,new_password):
        user=User.objects.get(id=user_id)
        if user is None:
            raise UserNotFoundException("User not found")
        if user.check_password(old_password):
            raise UserPasswordIsWrong
        user.set_password(new_password)
        user.save()
    