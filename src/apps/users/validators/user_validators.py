import re
from rest_framework import serializersel
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
import apps.users.serializers.user_serializers as user_serializers
from django.contrib.auth import get_user_model
@staticmethod
class HelperUserMethods:
    def validate_username(self, value):
        if not re.match(r'^[a-zA-Z0-9_]+$', value):
            raise user_serializers.ValidationError(
                "Username can only contain letters, numbers, and underscores"
            )
        if len(value) < 3:
            raise user_serializers.ValidationError(
                "Username must be at least 3 characters long"
            )
        return value
    def validate_phone_number(self, value):
        if not re.match(r'^\+?1?\d{9,15}$', value):
            raise user_serializers.ValidationError(
                "Phone number must be in format: '+999999999'. Up to 15 digits allowed."
            )
        return value
    def validate_password(self, value):
        try:
            validate_password(value)
        except DjangoValidationError as e:
            raise user_serializers.ValidationError(list(e.messages))

        if not re.search(r'[A-Za-z]', value):
            raise user_serializers.ValidationError(
                "Password must contain at least one letter"
            )

        if not re.search(r'\d', value):
            raise user_serializers.ValidationError(
                "Password must contain at least one digit"
            )
        return value
