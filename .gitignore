# Python
__pycache__/
*.py[cod]
*$py.class

# Django
*.log
*.pot
*.pyc
*.pyo
*.mo
*.db
db.sqlite3
media/
staticfiles/

# Environment
.env
*.env
.env.*.local
.venv/
venv/
ENV/

# IDEs and Editors
.vscode/
.idea/
*.swp
*~
.project
.pydevproject

# Byte-compiled / distribution
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Migrations (optional, ignore if you don’t want to commit)
# Uncomment if you don’t want migrations in git
# */migrations/*.py
# */migrations/*.pyc

# MyPy / Pylint / other type-checking
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/

# Local settings
local_settings.py
